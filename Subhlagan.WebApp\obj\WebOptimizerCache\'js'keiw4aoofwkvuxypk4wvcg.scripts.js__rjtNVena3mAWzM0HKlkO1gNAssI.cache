{"Headers": {"Last-Modified": "<PERSON><PERSON>, 16 May 2023 08:17:12 GMT", "X-Content-Type-Options": "nosniff"}, "Body": "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", "CacheKey": "rjtNVena3mAWzM0HKlkO1gNAssI"}