import { CustomLocale } from "../types/locale";
export declare const Dutch: CustomLocale;
declare const _default: {
    ar?: CustomLocale | undefined;
    at?: CustomLocale | undefined;
    az?: CustomLocale | undefined;
    be?: CustomLocale | undefined;
    bg?: CustomLocale | undefined;
    bn?: CustomLocale | undefined;
    bs?: CustomLocale | undefined;
    ca?: CustomLocale | undefined;
    cat?: CustomLocale | undefined;
    ckb?: CustomLocale | undefined;
    cs?: CustomLocale | undefined;
    cy?: CustomLocale | undefined;
    da?: CustomLocale | undefined;
    de?: CustomLocale | undefined;
    default?: CustomLocale | undefined;
    en?: CustomLocale | undefined;
    eo?: CustomLocale | undefined;
    es?: CustomLocale | undefined;
    et?: CustomLocale | undefined;
    fa?: CustomLocale | undefined;
    fi?: CustomLocale | undefined;
    fo?: CustomLocale | undefined;
    fr?: CustomLocale | undefined;
    gr?: CustomLocale | undefined;
    he?: CustomLocale | undefined;
    hi?: CustomLocale | undefined;
    hr?: CustomLocale | undefined;
    hu?: CustomLocale | undefined;
    hy?: CustomLocale | undefined;
    id?: CustomLocale | undefined;
    is?: CustomLocale | undefined;
    it?: CustomLocale | undefined;
    ja?: CustomLocale | undefined;
    ka?: CustomLocale | undefined;
    ko?: CustomLocale | undefined;
    km?: CustomLocale | undefined;
    kz?: CustomLocale | undefined;
    lt?: CustomLocale | undefined;
    lv?: CustomLocale | undefined;
    mk?: CustomLocale | undefined;
    mn?: CustomLocale | undefined;
    ms?: CustomLocale | undefined;
    my?: CustomLocale | undefined;
    nl?: CustomLocale | undefined;
    nn?: CustomLocale | undefined;
    no?: CustomLocale | undefined;
    pa?: CustomLocale | undefined;
    pl?: CustomLocale | undefined;
    pt?: CustomLocale | undefined;
    ro?: CustomLocale | undefined;
    ru?: CustomLocale | undefined;
    si?: CustomLocale | undefined;
    sk?: CustomLocale | undefined;
    sl?: CustomLocale | undefined;
    sq?: CustomLocale | undefined;
    sr?: CustomLocale | undefined;
    sv?: CustomLocale | undefined;
    th?: CustomLocale | undefined;
    tr?: CustomLocale | undefined;
    uk?: CustomLocale | undefined;
    vn?: CustomLocale | undefined;
    zh?: CustomLocale | undefined;
    uz?: CustomLocale | undefined;
    uz_latn?: CustomLocale | undefined;
    zh_tw?: CustomLocale | undefined;
} & {
    default: import("../types/locale").Locale;
};
export default _default;
