@model DateTime
@{
    var fieldName = ViewData.TemplateInfo.GetFullHtmlFieldName(string.Empty);
    var displayId = fieldName + "_Display";
    var value = Model.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
    var postValue = Model.ToString("yyyy-MM-dd");
}

<!-- Hidden field to post ISO format to backend -->
<input type="hidden" id="@fieldName" name="@fieldName" value="@postValue" />

<!-- Flatpickr Date Picker (Open Source Replacement) -->
<input id="@displayId" class="form-control flatpickr-input" type="text" placeholder="dd/mm/yyyy" readonly="readonly" />

<script>
    $(document).ready(function () {
        // Initialize Flatpickr with comprehensive configuration
        var picker = flatpickr("#@displayId", {
            dateFormat: "d/m/Y",
            altInput: false, // We handle the hidden field manually
            allowInput: false, // Prevent manual typing to avoid format issues
            defaultDate: "@value" || null,
            locale: {
                firstDayOfWeek: 1 // Monday as first day (common in many locales)
            },
            onChange: function(selectedDates, dateStr, instance) {
                if (selectedDates.length > 0) {
                    // Format date as ISO (yyyy-MM-dd) for backend
                    var isoDate = selectedDates[0].getFullYear() + '-' + 
                                String(selectedDates[0].getMonth() + 1).padStart(2, '0') + '-' + 
                                String(selectedDates[0].getDate()).padStart(2, '0');
                    $("#@fieldName").val(isoDate);
                } else {
                    $("#@fieldName").val(""); // Clear the hidden field when picker is cleared
                }
            },
            onReady: function(selectedDates, dateStr, instance) {
                // Ensure hidden field is set correctly on initialization
                if (selectedDates.length > 0) {
                    var isoDate = selectedDates[0].getFullYear() + '-' + 
                                String(selectedDates[0].getMonth() + 1).padStart(2, '0') + '-' + 
                                String(selectedDates[0].getDate()).padStart(2, '0');
                    $("#@fieldName").val(isoDate);
                }
            },
            // Allow clearing the date
            wrap: false,
            clickOpens: true
        });

        // Add clear functionality with a clear button
        var clearBtn = $('<button type="button" class="btn btn-outline-secondary btn-sm" style="margin-left: 5px;"><i class="fas fa-times"></i></button>');
        $("#@displayId").after(clearBtn);
        
        clearBtn.on('click', function(e) {
            e.preventDefault();
            picker.clear();
            $("#@fieldName").val("");
        });
    });
</script>