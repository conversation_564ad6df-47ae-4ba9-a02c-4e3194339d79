var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=function(e,i,r,n,a,s,l,o,c,u){this.numeralDecimalMark=e||".",this.numeralIntegerScale=i>0?i:0,this.numeralDecimalScale=r>=0?r:2,this.numeralThousandsGroupStyle=n||t.groupStyle.thousand,this.numeralPositiveOnly=!!a,this.stripLeadingZeroes=!1!==s,this.prefix=l||""===l?l:"",this.signBeforePrefix=!!o,this.tailPrefix=!!c,this.delimiter=u||""===u?u:",",this.delimiterRE=u?new RegExp("\\"+u,"g"):""};t.groupStyle={thousand:"thousand",lakh:"lakh",wan:"wan",none:"none"},t.prototype={getRawValue:function(e){return e.replace(this.delimiterRE,"").replace(this.numeralDecimalMark,".")},format:function(e){var i,r,n,a,s="";switch(e=e.replace(/[A-Za-z]/g,"").replace(this.numeralDecimalMark,"M").replace(/[^\dM-]/g,"").replace(/^\-/,"N").replace(/\-/g,"").replace("N",this.numeralPositiveOnly?"":"-").replace("M",this.numeralDecimalMark),this.stripLeadingZeroes&&(e=e.replace(/^(-)?0+(?=\d)/,"$1")),r="-"===e.slice(0,1)?"-":"",n=void 0!==this.prefix?this.signBeforePrefix?r+this.prefix:this.prefix+r:r,a=e,e.indexOf(this.numeralDecimalMark)>=0&&(a=(i=e.split(this.numeralDecimalMark))[0],s=this.numeralDecimalMark+i[1].slice(0,this.numeralDecimalScale)),"-"===r&&(a=a.slice(1)),this.numeralIntegerScale>0&&(a=a.slice(0,this.numeralIntegerScale)),this.numeralThousandsGroupStyle){case t.groupStyle.lakh:a=a.replace(/(\d)(?=(\d\d)+\d$)/g,"$1"+this.delimiter);break;case t.groupStyle.wan:a=a.replace(/(\d)(?=(\d{4})+$)/g,"$1"+this.delimiter);break;case t.groupStyle.thousand:a=a.replace(/(\d)(?=(\d{3})+$)/g,"$1"+this.delimiter)}return this.tailPrefix?r+a.toString()+(this.numeralDecimalScale>0?s.toString():"")+this.prefix:n+a.toString()+(this.numeralDecimalScale>0?s.toString():"")}};var i=t,r=function(e,t,i){this.date=[],this.blocks=[],this.datePattern=e,this.dateMin=t.split("-").reverse().map(function(e){return parseInt(e,10)}),2===this.dateMin.length&&this.dateMin.unshift(0),this.dateMax=i.split("-").reverse().map(function(e){return parseInt(e,10)}),2===this.dateMax.length&&this.dateMax.unshift(0),this.initBlocks()};r.prototype={initBlocks:function(){var e=this;e.datePattern.forEach(function(t){"Y"===t?e.blocks.push(4):e.blocks.push(2)})},getISOFormatDate:function(){var e=this.date;return e[2]?e[2]+"-"+this.addLeadingZero(e[1])+"-"+this.addLeadingZero(e[0]):""},getBlocks:function(){return this.blocks},getValidatedDate:function(e){var t=this,i="";return e=e.replace(/[^\d]/g,""),t.blocks.forEach(function(r,n){if(e.length>0){var a=e.slice(0,r),s=a.slice(0,1),l=e.slice(r);switch(t.datePattern[n]){case"d":"00"===a?a="01":parseInt(s,10)>3?a="0"+s:parseInt(a,10)>31&&(a="31");break;case"m":"00"===a?a="01":parseInt(s,10)>1?a="0"+s:parseInt(a,10)>12&&(a="12")}i+=a,e=l}}),this.getFixedDateString(i)},getFixedDateString:function(e){var t,i,r,n=this,a=n.datePattern,s=[],l=0,o=0,c=0,u=0,d=0,h=0,m=!1;return 4===e.length&&"y"!==a[0].toLowerCase()&&"y"!==a[1].toLowerCase()&&(d=2-(u="d"===a[0]?0:2),t=parseInt(e.slice(u,u+2),10),i=parseInt(e.slice(d,d+2),10),s=this.getFixedDate(t,i,0)),8===e.length&&(a.forEach(function(e,t){switch(e){case"d":l=t;break;case"m":o=t;break;default:c=t}}),h=2*c,u=l<=c?2*l:2*l+2,d=o<=c?2*o:2*o+2,t=parseInt(e.slice(u,u+2),10),i=parseInt(e.slice(d,d+2),10),r=parseInt(e.slice(h,h+4),10),m=4===e.slice(h,h+4).length,s=this.getFixedDate(t,i,r)),4!==e.length||"y"!==a[0]&&"y"!==a[1]||(h=2-(d="m"===a[0]?0:2),i=parseInt(e.slice(d,d+2),10),r=parseInt(e.slice(h,h+2),10),m=2===e.slice(h,h+2).length,s=[0,i,r]),6!==e.length||"Y"!==a[0]&&"Y"!==a[1]||(h=2-.5*(d="m"===a[0]?0:4),i=parseInt(e.slice(d,d+2),10),r=parseInt(e.slice(h,h+4),10),m=4===e.slice(h,h+4).length,s=[0,i,r]),s=n.getRangeFixedDate(s),n.date=s,0===s.length?e:a.reduce(function(e,t){switch(t){case"d":return e+(0===s[0]?"":n.addLeadingZero(s[0]));case"m":return e+(0===s[1]?"":n.addLeadingZero(s[1]));case"y":return e+(m?n.addLeadingZeroForYear(s[2],!1):"");case"Y":return e+(m?n.addLeadingZeroForYear(s[2],!0):"")}},"")},getRangeFixedDate:function(e){var t=this.datePattern,i=this.dateMin||[],r=this.dateMax||[];return!e.length||i.length<3&&r.length<3?e:t.find(function(e){return"y"===e.toLowerCase()})&&0===e[2]?e:r.length&&(r[2]<e[2]||r[2]===e[2]&&(r[1]<e[1]||r[1]===e[1]&&r[0]<e[0]))?r:i.length&&(i[2]>e[2]||i[2]===e[2]&&(i[1]>e[1]||i[1]===e[1]&&i[0]>e[0]))?i:e},getFixedDate:function(e,t,i){return e=Math.min(e,31),t=Math.min(t,12),i=parseInt(i||0,10),(t<7&&t%2==0||t>8&&t%2==1)&&(e=Math.min(e,2===t?this.isLeapYear(i)?29:28:30)),[e,t,i]},isLeapYear:function(e){return e%4==0&&e%100!=0||e%400==0},addLeadingZero:function(e){return(e<10?"0":"")+e},addLeadingZeroForYear:function(e,t){return t?(e<10?"000":e<100?"00":e<1e3?"0":"")+e:(e<10?"0":"")+e}};var n=r,a=function(e,t){this.time=[],this.blocks=[],this.timePattern=e,this.timeFormat=t,this.initBlocks()};a.prototype={initBlocks:function(){var e=this;e.timePattern.forEach(function(){e.blocks.push(2)})},getISOFormatTime:function(){var e=this.time;return e[2]?this.addLeadingZero(e[0])+":"+this.addLeadingZero(e[1])+":"+this.addLeadingZero(e[2]):""},getBlocks:function(){return this.blocks},getTimeFormatOptions:function(){return"12"===String(this.timeFormat)?{maxHourFirstDigit:1,maxHours:12,maxMinutesFirstDigit:5,maxMinutes:60}:{maxHourFirstDigit:2,maxHours:23,maxMinutesFirstDigit:5,maxMinutes:60}},getValidatedTime:function(e){var t=this,i="";e=e.replace(/[^\d]/g,"");var r=t.getTimeFormatOptions();return t.blocks.forEach(function(n,a){if(e.length>0){var s=e.slice(0,n),l=s.slice(0,1),o=e.slice(n);switch(t.timePattern[a]){case"h":parseInt(l,10)>r.maxHourFirstDigit?s="0"+l:parseInt(s,10)>r.maxHours&&(s=r.maxHours+"");break;case"m":case"s":parseInt(l,10)>r.maxMinutesFirstDigit?s="0"+l:parseInt(s,10)>r.maxMinutes&&(s=r.maxMinutes+"")}i+=s,e=o}}),this.getFixedTimeString(i)},getFixedTimeString:function(e){var t,i,r,n=this,a=n.timePattern,s=[],l=0,o=0,c=0,u=0,d=0,h=0;return 6===e.length&&(a.forEach(function(e,t){switch(e){case"s":l=2*t;break;case"m":o=2*t;break;case"h":c=2*t}}),h=c,d=o,u=l,t=parseInt(e.slice(u,u+2),10),i=parseInt(e.slice(d,d+2),10),r=parseInt(e.slice(h,h+2),10),s=this.getFixedTime(r,i,t)),4===e.length&&n.timePattern.indexOf("s")<0&&(a.forEach(function(e,t){switch(e){case"m":o=2*t;break;case"h":c=2*t}}),h=c,d=o,t=0,i=parseInt(e.slice(d,d+2),10),r=parseInt(e.slice(h,h+2),10),s=this.getFixedTime(r,i,t)),n.time=s,0===s.length?e:a.reduce(function(e,t){switch(t){case"s":return e+n.addLeadingZero(s[2]);case"m":return e+n.addLeadingZero(s[1]);case"h":return e+n.addLeadingZero(s[0])}},"")},getFixedTime:function(e,t,i){return i=Math.min(parseInt(i||0,10),60),t=Math.min(t,60),[e=Math.min(e,60),t,i]},addLeadingZero:function(e){return(e<10?"0":"")+e}};var s=a,l=function(e,t){this.delimiter=t||""===t?t:" ",this.delimiterRE=t?new RegExp("\\"+t,"g"):"",this.formatter=e};l.prototype={setFormatter:function(e){this.formatter=e},format:function(e){this.formatter.clear();for(var t,i="",r=!1,n=0,a=(e=(e=(e=e.replace(/[^\d+]/g,"")).replace(/^\+/,"B").replace(/\+/g,"").replace("B","+")).replace(this.delimiterRE,"")).length;n<a;n++)t=this.formatter.inputDigit(e.charAt(n)),/[\s()-]/g.test(t)?(i=t,r=!0):r||(i=t);return i=(i=i.replace(/[()]/g,"")).replace(/[\s-]/g,this.delimiter)}};var o=l,c={blocks:{uatp:[4,5,6],amex:[4,6,5],diners:[4,6,4],discover:[4,4,4,4],mastercard:[4,4,4,4],dankort:[4,4,4,4],instapayment:[4,4,4,4],jcb15:[4,6,5],jcb:[4,4,4,4],maestro:[4,4,4,4],visa:[4,4,4,4],mir:[4,4,4,4],unionPay:[4,4,4,4],general:[4,4,4,4]},re:{uatp:/^(?!1800)1\d{0,14}/,amex:/^3[47]\d{0,13}/,discover:/^(?:6011|65\d{0,2}|64[4-9]\d?)\d{0,12}/,diners:/^3(?:0([0-5]|9)|[689]\d?)\d{0,11}/,mastercard:/^(5[1-5]\d{0,2}|22[2-9]\d{0,1}|2[3-7]\d{0,2})\d{0,12}/,dankort:/^(5019|4175|4571)\d{0,12}/,instapayment:/^63[7-9]\d{0,13}/,jcb15:/^(?:2131|1800)\d{0,11}/,jcb:/^(?:35\d{0,2})\d{0,12}/,maestro:/^(?:5[0678]\d{0,2}|6304|67\d{0,2})\d{0,12}/,mir:/^220[0-4]\d{0,12}/,visa:/^4\d{0,15}/,unionPay:/^(62|81)\d{0,14}/},getStrictBlocks:function(e){var t=e.reduce(function(e,t){return e+t},0);return e.concat(19-t)},getInfo:function(e,t){var i=c.blocks,r=c.re;for(var n in t=!!t,r)if(r[n].test(e)){var a=i[n];return{type:n,blocks:t?this.getStrictBlocks(a):a}}return{type:"unknown",blocks:t?this.getStrictBlocks(i.general):i.general}}},u=c,d={noop:function(){},strip:function(e,t){return e.replace(t,"")},getPostDelimiter:function(e,t,i){if(0===i.length)return e.slice(-t.length)===t?t:"";var r="";return i.forEach(function(t){e.slice(-t.length)===t&&(r=t)}),r},getDelimiterREByDelimiter:function(e){return new RegExp(e.replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1"),"g")},getNextCursorPosition:function(e,t,i,r,n){return t.length===e?i.length:e+this.getPositionOffset(e,t,i,r,n)},getPositionOffset:function(e,t,i,r,n){var a,s,l;return a=this.stripDelimiters(t.slice(0,e),r,n),s=this.stripDelimiters(i.slice(0,e),r,n),0!==(l=a.length-s.length)?l/Math.abs(l):0},stripDelimiters:function(e,t,i){var r=this;if(0===i.length){var n=t?r.getDelimiterREByDelimiter(t):"";return e.replace(n,"")}return i.forEach(function(t){t.split("").forEach(function(t){e=e.replace(r.getDelimiterREByDelimiter(t),"")})}),e},headStr:function(e,t){return e.slice(0,t)},getMaxLength:function(e){return e.reduce(function(e,t){return e+t},0)},getPrefixStrippedValue:function(e,t,i,r,n,a,s,l,o){if(0===i)return e;if(e===t&&""!==e)return"";if(o&&"-"==e.slice(0,1)){var c="-"==r.slice(0,1)?r.slice(1):r;return"-"+this.getPrefixStrippedValue(e.slice(1),t,i,c,n,a,s,l,o)}if(r.slice(0,i)!==t&&!l)return s&&!r&&e?e:"";if(r.slice(-i)!==t&&l)return s&&!r&&e?e:"";var u=this.stripDelimiters(r,n,a);return e.slice(0,i)===t||l?e.slice(-i)!==t&&l?u.slice(0,-i-1):l?e.slice(0,-i):e.slice(i):u.slice(i)},getFirstDiffIndex:function(e,t){for(var i=0;e.charAt(i)===t.charAt(i);)if(""===e.charAt(i++))return-1;return i},getFormattedValue:function(e,t,i,r,n,a){var s="",l=n.length>0,o="";return 0===i?e:(t.forEach(function(t,c){if(e.length>0){var u=e.slice(0,t),d=e.slice(t);o=l?n[a?c-1:c]||o:r,a?(c>0&&(s+=o),s+=u):(s+=u,u.length===t&&c<i-1&&(s+=o)),e=d}}),s)},fixPrefixCursor:function(e,t,i,r){if(e){var n=e.value,a=i||r[0]||" ";if(e.setSelectionRange&&t&&!(t.length+a.length<=n.length)){var s=2*n.length;setTimeout(function(){e.setSelectionRange(s,s)},1)}}},checkFullSelection:function(e){try{return(window.getSelection()||document.getSelection()||{}).toString().length===e.length}catch(e){}return!1},setSelection:function(e,t,i){if(e===this.getActiveElement(i)&&!(e&&e.value.length<=t))if(e.createTextRange){var r=e.createTextRange();r.move("character",t),r.select()}else try{e.setSelectionRange(t,t)}catch(e){console.warn("The input element type does not support selection")}},getActiveElement:function(e){var t=e.activeElement;return t&&t.shadowRoot?this.getActiveElement(t.shadowRoot):t},isAndroid:function(){return navigator&&/android/i.test(navigator.userAgent)},isAndroidBackspaceKeydown:function(e,t){return!!(this.isAndroid()&&e&&t)&&t===e.slice(0,-1)}},h={assign:function(t,i){return i=i||{},(t=t||{}).creditCard=!!i.creditCard,t.creditCardStrictMode=!!i.creditCardStrictMode,t.creditCardType="",t.onCreditCardTypeChanged=i.onCreditCardTypeChanged||function(){},t.phone=!!i.phone,t.phoneRegionCode=i.phoneRegionCode||"AU",t.phoneFormatter={},t.time=!!i.time,t.timePattern=i.timePattern||["h","m","s"],t.timeFormat=i.timeFormat||"24",t.timeFormatter={},t.date=!!i.date,t.datePattern=i.datePattern||["d","m","Y"],t.dateMin=i.dateMin||"",t.dateMax=i.dateMax||"",t.dateFormatter={},t.numeral=!!i.numeral,t.numeralIntegerScale=i.numeralIntegerScale>0?i.numeralIntegerScale:0,t.numeralDecimalScale=i.numeralDecimalScale>=0?i.numeralDecimalScale:2,t.numeralDecimalMark=i.numeralDecimalMark||".",t.numeralThousandsGroupStyle=i.numeralThousandsGroupStyle||"thousand",t.numeralPositiveOnly=!!i.numeralPositiveOnly,t.stripLeadingZeroes=!1!==i.stripLeadingZeroes,t.signBeforePrefix=!!i.signBeforePrefix,t.tailPrefix=!!i.tailPrefix,t.swapHiddenInput=!!i.swapHiddenInput,t.numericOnly=t.creditCard||t.date||!!i.numericOnly,t.uppercase=!!i.uppercase,t.lowercase=!!i.lowercase,t.prefix=t.creditCard||t.date?"":i.prefix||"",t.noImmediatePrefix=!!i.noImmediatePrefix,t.prefixLength=t.prefix.length,t.rawValueTrimPrefix=!!i.rawValueTrimPrefix,t.copyDelimiter=!!i.copyDelimiter,t.initValue=void 0!==i.initValue&&null!==i.initValue?i.initValue.toString():"",t.delimiter=i.delimiter||""===i.delimiter?i.delimiter:i.date?"/":i.time?":":i.numeral?",":(i.phone," "),t.delimiterLength=t.delimiter.length,t.delimiterLazyShow=!!i.delimiterLazyShow,t.delimiters=i.delimiters||[],t.blocks=i.blocks||[],t.blocksLength=t.blocks.length,t.root="object"==typeof e&&e?e:window,t.document=i.document||t.root.document,t.maxLength=0,t.backspace=!1,t.result="",t.onValueChanged=i.onValueChanged||function(){},t}},m=function(e,t){var i=!1;if("string"==typeof e?(this.element=document.querySelector(e),i=document.querySelectorAll(e).length>1):void 0!==e.length&&e.length>0?(this.element=e[0],i=e.length>1):this.element=e,!this.element)throw new Error("[cleave.js] Please check the element");if(i)try{console.warn("[cleave.js] Multiple input fields matched, cleave.js will only take the first one.")}catch(e){}t.initValue=this.element.value,this.properties=m.DefaultProperties.assign({},t),this.init()};m.prototype={init:function(){var e=this.properties;e.numeral||e.phone||e.creditCard||e.time||e.date||0!==e.blocksLength||e.prefix?(e.maxLength=m.Util.getMaxLength(e.blocks),this.isAndroid=m.Util.isAndroid(),this.lastInputValue="",this.isBackward="",this.onChangeListener=this.onChange.bind(this),this.onKeyDownListener=this.onKeyDown.bind(this),this.onFocusListener=this.onFocus.bind(this),this.onCutListener=this.onCut.bind(this),this.onCopyListener=this.onCopy.bind(this),this.initSwapHiddenInput(),this.element.addEventListener("input",this.onChangeListener),this.element.addEventListener("keydown",this.onKeyDownListener),this.element.addEventListener("focus",this.onFocusListener),this.element.addEventListener("cut",this.onCutListener),this.element.addEventListener("copy",this.onCopyListener),this.initPhoneFormatter(),this.initDateFormatter(),this.initTimeFormatter(),this.initNumeralFormatter(),(e.initValue||e.prefix&&!e.noImmediatePrefix)&&this.onInput(e.initValue)):this.onInput(e.initValue)},initSwapHiddenInput:function(){if(this.properties.swapHiddenInput){var e=this.element.cloneNode(!0);this.element.parentNode.insertBefore(e,this.element),this.elementSwapHidden=this.element,this.elementSwapHidden.type="hidden",this.element=e,this.element.id=""}},initNumeralFormatter:function(){var e=this.properties;e.numeral&&(e.numeralFormatter=new m.NumeralFormatter(e.numeralDecimalMark,e.numeralIntegerScale,e.numeralDecimalScale,e.numeralThousandsGroupStyle,e.numeralPositiveOnly,e.stripLeadingZeroes,e.prefix,e.signBeforePrefix,e.tailPrefix,e.delimiter))},initTimeFormatter:function(){var e=this.properties;e.time&&(e.timeFormatter=new m.TimeFormatter(e.timePattern,e.timeFormat),e.blocks=e.timeFormatter.getBlocks(),e.blocksLength=e.blocks.length,e.maxLength=m.Util.getMaxLength(e.blocks))},initDateFormatter:function(){var e=this.properties;e.date&&(e.dateFormatter=new m.DateFormatter(e.datePattern,e.dateMin,e.dateMax),e.blocks=e.dateFormatter.getBlocks(),e.blocksLength=e.blocks.length,e.maxLength=m.Util.getMaxLength(e.blocks))},initPhoneFormatter:function(){var e=this.properties;if(e.phone)try{e.phoneFormatter=new m.PhoneFormatter(new e.root.Cleave.AsYouTypeFormatter(e.phoneRegionCode),e.delimiter)}catch(e){throw new Error("[cleave.js] Please include phone-type-formatter.{country}.js lib")}},onKeyDown:function(e){var t=e.which||e.keyCode;this.lastInputValue=this.element.value,this.isBackward=8===t},onChange:function(e){var t=this.properties,i=m.Util;this.isBackward=this.isBackward||"deleteContentBackward"===e.inputType;var r=i.getPostDelimiter(this.lastInputValue,t.delimiter,t.delimiters);this.isBackward&&r?t.postDelimiterBackspace=r:t.postDelimiterBackspace=!1,this.onInput(this.element.value)},onFocus:function(){var e=this.properties;this.lastInputValue=this.element.value,e.prefix&&e.noImmediatePrefix&&!this.element.value&&this.onInput(e.prefix),m.Util.fixPrefixCursor(this.element,e.prefix,e.delimiter,e.delimiters)},onCut:function(e){m.Util.checkFullSelection(this.element.value)&&(this.copyClipboardData(e),this.onInput(""))},onCopy:function(e){m.Util.checkFullSelection(this.element.value)&&this.copyClipboardData(e)},copyClipboardData:function(e){var t=this.properties,i=m.Util,r=this.element.value,n="";n=t.copyDelimiter?r:i.stripDelimiters(r,t.delimiter,t.delimiters);try{e.clipboardData?e.clipboardData.setData("Text",n):window.clipboardData.setData("Text",n),e.preventDefault()}catch(e){}},onInput:function(e){var t=this.properties,i=m.Util,r=i.getPostDelimiter(e,t.delimiter,t.delimiters);return t.numeral||!t.postDelimiterBackspace||r||(e=i.headStr(e,e.length-t.postDelimiterBackspace.length)),t.phone?(!t.prefix||t.noImmediatePrefix&&!e.length?t.result=t.phoneFormatter.format(e):t.result=t.prefix+t.phoneFormatter.format(e).slice(t.prefix.length),void this.updateValueState()):t.numeral?(t.prefix&&t.noImmediatePrefix&&0===e.length?t.result="":t.result=t.numeralFormatter.format(e),void this.updateValueState()):(t.date&&(e=t.dateFormatter.getValidatedDate(e)),t.time&&(e=t.timeFormatter.getValidatedTime(e)),e=i.stripDelimiters(e,t.delimiter,t.delimiters),e=i.getPrefixStrippedValue(e,t.prefix,t.prefixLength,t.result,t.delimiter,t.delimiters,t.noImmediatePrefix,t.tailPrefix,t.signBeforePrefix),e=t.numericOnly?i.strip(e,/[^\d]/g):e,e=t.uppercase?e.toUpperCase():e,e=t.lowercase?e.toLowerCase():e,t.prefix&&(t.tailPrefix?e+=t.prefix:e=t.prefix+e,0===t.blocksLength)?(t.result=e,void this.updateValueState()):(t.creditCard&&this.updateCreditCardPropsByValue(e),e=i.headStr(e,t.maxLength),t.result=i.getFormattedValue(e,t.blocks,t.blocksLength,t.delimiter,t.delimiters,t.delimiterLazyShow),void this.updateValueState()))},updateCreditCardPropsByValue:function(e){var t,i=this.properties,r=m.Util;r.headStr(i.result,4)!==r.headStr(e,4)&&(t=m.CreditCardDetector.getInfo(e,i.creditCardStrictMode),i.blocks=t.blocks,i.blocksLength=i.blocks.length,i.maxLength=r.getMaxLength(i.blocks),i.creditCardType!==t.type&&(i.creditCardType=t.type,i.onCreditCardTypeChanged.call(this,i.creditCardType)))},updateValueState:function(){var e=this,t=m.Util,i=e.properties;if(e.element){var r=e.element.selectionEnd,n=e.element.value,a=i.result;r=t.getNextCursorPosition(r,n,a,i.delimiter,i.delimiters),e.isAndroid?window.setTimeout(function(){e.element.value=a,t.setSelection(e.element,r,i.document,!1),e.callOnValueChanged()},1):(e.element.value=a,i.swapHiddenInput&&(e.elementSwapHidden.value=e.getRawValue()),t.setSelection(e.element,r,i.document,!1),e.callOnValueChanged())}},callOnValueChanged:function(){var e=this.properties;e.onValueChanged.call(this,{target:{name:this.element.name,value:e.result,rawValue:this.getRawValue()}})},setPhoneRegionCode:function(e){this.properties.phoneRegionCode=e,this.initPhoneFormatter(),this.onChange()},setRawValue:function(e){var t=this.properties;e=null!=e?e.toString():"",t.numeral&&(e=e.replace(".",t.numeralDecimalMark)),t.postDelimiterBackspace=!1,this.element.value=e,this.onInput(e)},getRawValue:function(){var e=this.properties,t=m.Util,i=this.element.value;return e.rawValueTrimPrefix&&(i=t.getPrefixStrippedValue(i,e.prefix,e.prefixLength,e.result,e.delimiter,e.delimiters,e.noImmediatePrefix,e.tailPrefix,e.signBeforePrefix)),i=e.numeral?e.numeralFormatter.getRawValue(i):t.stripDelimiters(i,e.delimiter,e.delimiters)},getISOFormatDate:function(){var e=this.properties;return e.date?e.dateFormatter.getISOFormatDate():""},getISOFormatTime:function(){var e=this.properties;return e.time?e.timeFormatter.getISOFormatTime():""},getFormattedValue:function(){return this.element.value},destroy:function(){this.element.removeEventListener("input",this.onChangeListener),this.element.removeEventListener("keydown",this.onKeyDownListener),this.element.removeEventListener("focus",this.onFocusListener),this.element.removeEventListener("cut",this.onCutListener),this.element.removeEventListener("copy",this.onCopyListener)},toString:function(){return"[Cleave Object]"}},m.NumeralFormatter=i,m.DateFormatter=n,m.TimeFormatter=s,m.PhoneFormatter=o,m.CreditCardDetector=u,m.Util=d,m.DefaultProperties=h,("object"==typeof e&&e?e:window).Cleave=m;export default m;