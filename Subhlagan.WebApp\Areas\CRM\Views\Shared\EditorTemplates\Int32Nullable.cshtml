﻿@model Int32?
<input asp-for="@Model" />
@{
    var postfix = "";
    if (ViewData.ContainsKey("postfix") && ViewData["postfix"] != null)
    {
        postfix = ViewData["postfix"].ToString();
    }
    var culture = CultureInfo.CurrentCulture.TextInfo.IsRightToLeft ? CultureInfo.InvariantCulture : CultureInfo.CurrentCulture;
}
<script>
    $(document).ready(function() {
        // Enhanced HTML5 number input for Nullable Int32 with Cleave.js formatting (Open Source Replacement)
        var element = $("#@Html.IdForModel()");
        
        // Configure input as number type with step=1 for integers
        element.attr({
            'type': 'number',
            'step': '1',
            'class': element.attr('class') + ' form-control numeric-input nullable-int'
        });
        
        // Optional: Add postfix display after the input
        var postfix = "@Html.Raw(postfix)";
        if (postfix) {
            element.after('<span class="input-group-text numeric-postfix">' + postfix + '</span>');
            element.wrap('<div class="input-group"></div>');
        }
        
        // Initialize Cleave.js for number formatting (nullable)
        new Cleave('#@Html.IdForModel()', {
            numeral: true,
            numeralDecimalMark: '.',
            numeralThousandsGroupStyle: 'thousand',
            numeralDecimalScale: 0, // No decimals for integers
            numeralPositiveOnly: false, // Allow negative numbers
            onValueChanged: function (e) {
                // Ensure the actual numeric value is maintained (handle nullable)
                var numericValue = e.target.rawValue;
                element.attr('data-numeric-value', numericValue || '');
            }
        });
        
        // Handle form submission - ensure we submit the raw numeric value (or empty for null)
        element.closest('form').on('submit', function() {
            var rawValue = element.attr('data-numeric-value');
            if (rawValue === '' || rawValue === null || rawValue === undefined) {
                element.val(''); // Submit empty value for null
            } else {
                element.val(rawValue.replace(/[^\d.-]/g, ''));
            }
        });
        
        // Add clear functionality for nullable inputs
        if (postfix) {
            var clearBtn = $('<button type="button" class="btn btn-outline-secondary btn-sm" style="margin-left: 5px;" title="Clear value"><i class="fas fa-times"></i></button>');
            element.parent().after(clearBtn);
            
            clearBtn.on('click', function(e) {
                e.preventDefault();
                element.val('');
                element.attr('data-numeric-value', '');
            });
        }
    });
</script>