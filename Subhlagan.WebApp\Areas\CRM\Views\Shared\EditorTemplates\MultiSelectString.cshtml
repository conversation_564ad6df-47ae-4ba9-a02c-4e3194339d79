﻿@model List<string>
<select asp-for="@Model" asp-items="@((IEnumerable<SelectListItem>)ViewData["SelectList"])"></select>
<script>
    $(document).ready(function () {
        // Enhanced Select2 Multi-Select for Strings (Open Source Replacement)
        var element = $("#@Html.IdForModel()");
        
        // Add multiple attribute to select element
        element.attr('multiple', 'multiple');
        
        // Configure Select2 for multiple string selection with static data
        element.select2({
            theme: 'bootstrap4', // Use Bootstrap 4 theme for consistency
            placeholder: 'Select options...', // Better UX for multi-select
            allowClear: true, // Allow clearing all selections
            width: '100%', // Responsive width
            closeOnSelect: false // Keep dropdown open for multiple selections
        });
        
        // Handle selection events with special logic for empty string values
        element.on('select2:select', function (e) {
            var data = e.params.data;
            var currentValues = element.val() || [];
            
            // Special handling for empty string value (representing "none" or "all")
            if (!data.id || data.id.trim() === '') {
                // If empty string is selected, clear all other selections
                element.val([""]).trigger('change');
            } else {
                // If other items are selected, remove empty string from selection
                var hasEmptyString = currentValues.some(function(value) {
                    return !value || value.trim() === '';
                });
                
                if (hasEmptyString) {
                    var filteredValues = currentValues.filter(function(value) {
                        return value && value.trim() !== '';
                    });
                    filteredValues.push(data.id);
                    element.val(filteredValues).trigger('change');
                }
            }
        });
        
        // Handle unselection events
        element.on('select2:unselect', function (e) {
            var currentValues = element.val() || [];
            
            // If no items are selected after unselecting, default to empty string
            if (currentValues.length === 0) {
                element.val([""]).trigger('change');
            }
        });
        
        // Handle clearing all selections
        element.on('select2:clear', function (e) {
            // Default to empty string if no items are selected
            element.val([""]).trigger('change');
        });
        
        // Ensure proper initial state
        var initialValues = element.val() || [];
        if (initialValues.length === 0) {
            element.val([""]).trigger('change');
        }
    });
</script>