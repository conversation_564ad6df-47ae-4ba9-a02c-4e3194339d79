(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.no = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Norwegian = {
      weekdays: {
          shorthand: ["<PERSON>øn", "Man", "Tir", "Ons", "Tor", "Fre", "<PERSON>ør"],
          longhand: [
              "Søndag",
              "Mandag",
              "Tirsdag",
              "Onsdag",
              "Torsdag",
              "Fredag",
              "Lørdag",
          ],
      },
      months: {
          shorthand: [
              "Jan",
              "Feb",
              "Mar",
              "Apr",
              "Mai",
              "Jun",
              "Jul",
              "Aug",
              "Sep",
              "Okt",
              "Nov",
              "<PERSON>",
          ],
          longhand: [
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON>",
              "April",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "August",
              "September",
              "<PERSON>to<PERSON>",
              "November",
              "Desember",
          ],
      },
      firstDayOfWeek: 1,
      rangeSeparator: " til ",
      weekAbbreviation: "Uke",
      scrollTitle: "Scroll for å endre",
      toggleTitle: "Klikk for å veksle",
      time_24hr: true,
      ordinal: function () {
          return ".";
      },
  };
  fp.l10ns.no = Norwegian;
  var no = fp.l10ns;

  exports.Norwegian = Norwegian;
  exports.default = no;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
