using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.EventLog;
using Subhlagan.BackupService.Models;
using Subhlagan.BackupService.Services;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace Subhlagan.BackupService
{
    public class Program
    {
        public static async Task Main(string[] args)
        {

            try
            {
                var builder = CreateHostBuilder(args);
                var host = builder.Build();

                // Start the host first to ensure Windows Service reports "started" status quickly
                await host.StartAsync();

                // Perform health check after service has started (non-blocking for service startup)
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // Wait a moment for services to initialize
                        await Task.Delay(2000);
                        await PerformStartupHealthCheckAsync(host);
                    }
                    catch (Exception ex)
                    {
                        var logger = host.Services.GetService<ILogger<Program>>();
                        logger?.LogError(ex, "Health check failed during background execution");
                    }
                });

                // Wait for the host to complete
                await host.WaitForShutdownAsync();
            }
            catch (Exception ex)
            {
                // Log to Event Log if possible, otherwise console
                await LogCriticalErrorAsync(ex);
                Environment.Exit(1);
            }
        }

       
        private static async Task LogCriticalErrorAsync(Exception ex)
        {
            try
            {
                // Try Event Log first (Windows Service)
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows) && !Environment.UserInteractive)
                {
                    using var eventLog = new EventLog("Application");
                    eventLog.Source = "Subhlagan Database Backup Service";
                    eventLog.WriteEntry($"Critical startup error: {ex.Message}\n\nStack Trace:\n{ex.StackTrace}", 
                        EventLogEntryType.Error);
                }
            }
            catch
            {
                // Fallback to console/file
            }

            try
            {
                // Try file logging
                var logDir = Path.Combine(AppContext.BaseDirectory, "Logs");
                Directory.CreateDirectory(logDir);
                var logFile = Path.Combine(logDir, $"critical-error-{DateTime.Now:yyyy-MM-dd}.log");
                await File.AppendAllTextAsync(logFile, 
                    $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} CRITICAL: {ex.Message}\n{ex.StackTrace}\n\n");
            }
            catch
            {
                // Final fallback to console
                Console.WriteLine($"Critical startup error: {ex.Message}");
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args)
        {
            var builder = Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration((context, config) =>
                {
                    // Use the application's base directory instead of current working directory
                    // This fixes issues when running as Windows Service where current directory is system32
                    var basePath = AppContext.BaseDirectory;
                    config.SetBasePath(basePath);
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                    config.AddEnvironmentVariables();
                    config.AddCommandLine(args);
                })
                .ConfigureServices((context, services) =>
                {
                    // Configure backup settings
                    services.Configure<BackupSettings>(
                        context.Configuration.GetSection("BackupSettings"));

                    // Register services
                    services.AddSingleton<IDatabaseBackupService, DatabaseBackupService>();
                    services.AddSingleton<IBackupCleanupService, BackupCleanupService>();
                    services.AddSingleton<ServiceHealthChecker>();

                    // Register the hosted service
                    services.AddHostedService<BackupHostedService>();

                    // Add HTTP client if needed for external monitoring
                    services.AddHttpClient();
                })
                .ConfigureLogging((context, logging) =>
                {
                    logging.ClearProviders();
                    
                    // Check if running in console mode (interactive) vs Windows Service mode
                    bool isConsoleMode = Environment.UserInteractive || 
                                       context.HostingEnvironment.IsDevelopment() ||
                                       args.Contains("--console");

                    // For Windows Service: Start with minimal logging to avoid startup issues
                    if (!isConsoleMode)
                    {
                        // Only add basic console logging during service startup
                        // Event Log and File logging will be added after service starts
                        logging.SetMinimumLevel(LogLevel.Warning);
                    }
                    else
                    {
                        // Add full console logging for interactive mode
                        logging.AddConsole(options =>
                        {
                            options.IncludeScopes = true;
                            options.TimestampFormat = "[yyyy-MM-dd HH:mm:ss] ";
                        });
                        
                        if (context.HostingEnvironment.IsDevelopment())
                        {
                            logging.AddDebug();
                        }
                        
                        logging.SetMinimumLevel(LogLevel.Information);
                    }

                    // Set specific log levels for different categories
                    logging.AddFilter("Microsoft", LogLevel.Warning);
                    logging.AddFilter("System", LogLevel.Warning);
                });

            // Configure as Windows Service if running on Windows
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                builder.UseWindowsService(options =>
                {
                    options.ServiceName = "SubhlaganDatabaseBackup";
                });
            }

            return builder;
        }

        private static async Task PerformStartupHealthCheckAsync(IHost host)
        {
            using var scope = host.Services.CreateScope();
            var healthChecker = scope.ServiceProvider.GetRequiredService<ServiceHealthChecker>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

            logger.LogInformation("Performing startup health check...");

            try
            {
                var isHealthy = await healthChecker.PerformHealthCheckAsync();

                if (isHealthy)
                {
                    logger.LogInformation("Startup health check PASSED - Service is ready to operate");
                }
                else
                {
                    logger.LogWarning("Startup health check FAILED - Service may not operate correctly");
                    // Continue anyway, but with warning
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error during startup health check");
                // Continue anyway, health check failure shouldn't prevent service start
            }
        }
    }

    /// <summary>
    /// Simple file logger provider for backup service logging (used only after service initialization)
    /// </summary>
    public class FileLoggerProvider : ILoggerProvider
    {
        private readonly string _logDirectory;

        public FileLoggerProvider(string logDirectory)
        {
            _logDirectory = logDirectory;
            try
            {
                Directory.CreateDirectory(_logDirectory);
            }
            catch
            {
                // Ignore directory creation errors during service startup
            }
        }

        public ILogger CreateLogger(string categoryName)
        {
            return new FileLogger(categoryName, _logDirectory);
        }

        public void Dispose()
        {
        }
    }

    /// <summary>
    /// Simple file logger implementation that doesn't block service startup
    /// </summary>
    public class FileLogger : ILogger
    {
        private readonly string _categoryName;
        private readonly string _logDirectory;

        public FileLogger(string categoryName, string logDirectory)
        {
            _categoryName = categoryName;
            _logDirectory = logDirectory;
        }

        public IDisposable BeginScope<TState>(TState state) => null!;

        public bool IsEnabled(LogLevel logLevel) => logLevel >= LogLevel.Information;

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
        {
            if (!IsEnabled(logLevel))
                return;

            // Fire and forget to avoid blocking
            _ = Task.Run(() =>
            {
                try
                {
                    var logFile = Path.Combine(_logDirectory, $"backup-service-{DateTime.Now:yyyy-MM-dd}.log");
                    var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} [{logLevel}] [{_categoryName}] {formatter(state, exception)}";
                    
                    if (exception != null)
                    {
                        logEntry += Environment.NewLine + exception.ToString();
                    }

                    File.AppendAllText(logFile, logEntry + Environment.NewLine);
                }
                catch
                {
                    // Swallow all exceptions to prevent service startup issues
                }
            });
        }
    }
}