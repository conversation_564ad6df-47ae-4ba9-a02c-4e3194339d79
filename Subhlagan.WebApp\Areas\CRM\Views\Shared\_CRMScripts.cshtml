@inject IKsFileProvider fileProvider;
@inject IWebHelper webHelper
@inject LocalizationSettings localizationSettings
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment WebHostEnvironment

@using System.Globalization
@using Subhlagan.Core.Domain.Localization

@{
    //Replacement UI Libraries (Open Source)
    // Removed Kendo UI (commercial license) - replaced with open source alternatives
    var supportRtl = (await workContext.GetWorkingLanguageAsync()).Rtl && !localizationSettings.IgnoreRtlPropertyForAdminArea;

    var culture = CultureInfo.CurrentCulture;
    var uiCulture = CultureInfo.CurrentUICulture;

    //Code to get check if current cultures scripts are exists. If not, select parent cultures scripts
    string GetDefaultCulture()
    {
        var localePattern = KsCommonDefaults.LocalePatternPath;

        var cultureToUse = KsCommonDefaults.DefaultLocalePattern; //Default regionalisation to use

        if (fileProvider.DirectoryExists(fileProvider.Combine(WebHostEnvironment.WebRootPath, string.Format(localePattern, culture.Name))))
            cultureToUse = culture.Name;
        else if (fileProvider.DirectoryExists(fileProvider.Combine(WebHostEnvironment.WebRootPath, string.Format(localePattern, culture.TwoLetterISOLanguageName))))
            cultureToUse = culture.TwoLetterISOLanguageName;

        return cultureToUse;
    }
}

@*Google Font*@
<!link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic" />

@* CSS resources *@
<link rel="stylesheet" href="~/lib_npm/jquery-ui-dist/jquery-ui.min.css"/>
<link rel="stylesheet" href="~/lib_npm/bootstrap-touchspin/jquery.bootstrap-touchspin.min.css"/>
<link rel="stylesheet" href="~/lib_npm/@("@fortawesome")/fontawesome-free/css/all.min.css"/>
@* Replacement UI Library Styles (Open Source) *@
<link rel="stylesheet" href="~/lib_npm/flatpickr/dist/flatpickr.min.css"/>
<link rel="stylesheet" href="~/lib_npm/select2/dist/css/select2.min.css"/>
<link rel="stylesheet" href="~/lib_npm/select2-bootstrap4-theme/dist/select2-bootstrap4.min.css"/>
<link rel="stylesheet" href="~/lib_npm/datatables.net-bs4/css/dataTables.bootstrap4.min.css"/>
<link rel="stylesheet" href="~/lib_npm/datatables.net-buttons-bs4/css/buttons.bootstrap4.min.css"/>
<link rel="stylesheet" href="~/lib_npm/overlayscrollbars/css/OverlayScrollbars.min.css"/>

@if (supportRtl)
{
    <link rel="stylesheet" href="~/lib_npm/@("@laylazi")/bootstrap-rtl/css/bootstrap-rtl.min.css"/>
    <link rel="stylesheet" href="~/lib/adminLTE/css/adminlte-rtl.min.css"/>
    <link rel="stylesheet" href="~/css/crmsubh/styles.rtl.css"/>
}
else
{
    <link rel="stylesheet" href="~/lib_npm/admin-lte/css/adminlte.min.css"/>
    <link rel="stylesheet" href="~/css/crmsubh/styles.css" />
    <link rel="stylesheet" href="~/css/crmsubh/snackbar.css" />
}

@KsHtml.GenerateCssFiles()

@* scripts *@
<script asp-location="None" src="~/lib_npm/jquery/jquery.min.js"></script>
<script src="~/lib_npm/jquery-ui-dist/jquery-ui.min.js"></script>
<script src="~/lib_npm/admin-lte/js/adminlte.min.js"></script>
<script src="~/lib_npm/overlayscrollbars/js/jquery.overlayScrollbars.min.js"></script>
<script src="~/lib_npm/bootstrap-touchspin/jquery.bootstrap-touchspin.min.js"></script>
<script src="~/lib_npm/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="~/lib_npm/jquery-validation/jquery.validate.min.js"></script>
<script src="~/lib_npm/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>

@* cldr scripts (needed for globalize) *@
<script src="~/lib_npm/cldrjs/cldr.js"></script>
<script src="~/lib_npm/cldrjs/cldr/event.js"></script>
<script src="~/lib_npm/cldrjs/cldr/supplemental.js"></script>

@* globalize scripts *@
<script src="~/lib_npm/globalize/globalize.js"></script>
<script src="~/lib_npm/globalize/globalize/number.js"></script>
<script src="~/lib_npm/globalize/globalize/date.js"></script>
<script src="~/lib_npm/globalize/globalize/currency.js"></script>

@* this file can be downloaded from :
    https://github.com/johnnyreilly/jquery-validation-globalize *@
<script src="~/lib/jquery-validation-globalize/jquery.validate.globalize.min.js"></script>

<script src="~/lib_npm/jquery-migrate/jquery-migrate.min.js"></script>
<script src="~/lib_npm/typeahead.js/typeahead.bundle.min.js"></script>
<script src="~/js/crm.common.js"></script>
<script src="~/js/crm.navigation.js"></script>
<script src="~/js/crm/snackbar.js"></script>
<script src="~/js/crm/crm.matchprofile.js"></script>
<script src="~/js/crm/public.matchprofile.js"></script>
@* Replacement UI Library Scripts (Open Source) *@
<script src="~/lib_npm/flatpickr/dist/flatpickr.min.js"></script>
<script src="~/lib_npm/select2/dist/js/select2.full.min.js"></script>
<script src="~/lib_npm/cleave.js/dist/cleave.min.js"></script>

@* Flatpickr Localization Support *@
@{ 
    var flatpickrLocale = culture.TwoLetterISOLanguageName != "en" ? culture.TwoLetterISOLanguageName : null;
    var localeFile = $"flatpickr/dist/l10n/{flatpickrLocale}.js";
    var localeExists = flatpickrLocale != null && fileProvider.FileExists(fileProvider.Combine(WebHostEnvironment.WebRootPath, "lib_npm", localeFile));
}
@if (localeExists)
{
    <script src="~/lib_npm/@localeFile"></script>
}
<script src="~/lib_npm/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="~/lib_npm/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="~/lib_npm/moment/min/moment-with-locales.min.js"></script>
<script src="~/lib_npm/datatables.net-buttons/js/dataTables.buttons.min.js"></script>
<script src="~/lib_npm/datatables.net-buttons-bs4/js/buttons.bootstrap4.min.js"></script>
<script asp-location="Footer">
        var rootAppPath = '@(Url.Content("~/"))';
        var culture = "@GetDefaultCulture()";

        //load cldr for current culture
        $.when(
            $.get({ url: rootAppPath + "lib_npm/cldr-data/supplemental/likelySubtags.json", dataType: "json"}),
            $.get({ url: rootAppPath + "lib_npm/cldr-data/main/" + culture + "/numbers.json", dataType: "json"}),
            $.get({ url: rootAppPath + "lib_npm/cldr-data/main/" + culture + "/currencies.json", dataType: "json"}),
            $.get({ url: rootAppPath + "lib_npm/cldr-data/supplemental/numberingSystems.json", dataType: "json"}),
            $.get({ url: rootAppPath + "lib_npm/cldr-data/main/" + culture + "/ca-gregorian.json", dataType: "json"}),
            $.get({ url: rootAppPath + "lib_npm/cldr-data/main/" + culture + "/timeZoneNames.json", dataType: "json"}),
            $.get({ url: rootAppPath + "lib_npm/cldr-data/supplemental/timeData.json", dataType: "json"}),
            $.get({ url: rootAppPath + "lib_npm/cldr-data/supplemental/weekData.json", dataType: "json"}),
        ).then(function () {
            // Normalize $.get results, we only need the JSON, not the request statuses.
            return [].slice.apply(arguments, [0]).map(function (result) {
                return result[0];
            });
        }).then(Globalize.load).then(function () {
            Globalize.locale(culture);
        });
</script>
<script asp-location="Footer">
        // Initialize replacement UI libraries with current culture
        @if (localeExists)
        {
            <text>if (typeof flatpickr !== 'undefined' && flatpickr.l10ns['@culture.TwoLetterISOLanguageName']) {
                flatpickr.localize(flatpickr.l10ns['@culture.TwoLetterISOLanguageName']);
            }</text>
        }
        
        // Set global Select2 language if available
        if (typeof $.fn.select2 !== 'undefined') {
            $.fn.select2.defaults.set('language', '@culture.Name');
        }
</script>

@KsHtml.GenerateScripts(ResourceLocation.Head)
@KsHtml.GenerateInlineScripts(ResourceLocation.Head)