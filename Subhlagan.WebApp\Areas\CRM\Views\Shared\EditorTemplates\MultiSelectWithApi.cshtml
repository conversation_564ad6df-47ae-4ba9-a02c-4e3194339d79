﻿@model List<int>

<select asp-for="@Model" asp-items="@((IEnumerable<SelectListItem>)ViewData["SelectList"])"></select>

<script>
    $(document).ready(function () {
        // Enhanced Select2 Multi-Select with API Integration (Open Source Replacement)
        var element = $("#@Html.IdForModel()");
        
        // Add multiple attribute to select element
        element.attr('multiple', 'multiple');
        
        // Configure Select2 with comprehensive AJAX support for multiple selection
        element.select2({
            theme: 'bootstrap4', // Use Bootstrap 4 theme for consistency
            placeholder: 'Select options...', // Better UX for multi-select
            allowClear: true, // Allow clearing all selections
            width: '100%', // Responsive width
            minimumInputLength: 1, // Start searching after 1 character
            closeOnSelect: false, // Keep dropdown open for multiple selections
            ajax: {
                url: "@(Url.RouteUrl(ViewData["ApiUrl"].ToString()))",
                type: "POST",
                dataType: 'json',
                delay: 250, // Debounce API calls
                data: function (params) {
                    var postData = {
                        term: params.term || '', // Search term
                        page: params.page || 1   // Pagination support
                    };
                    
                    // If AjaxParams was provided, merge them into postData
                    var additional = @Html.Raw(ViewData["AjaxParams"] ?? "{}");
                    $.extend(postData, additional);
                    
                    addAntiForgeryToken(postData); // Add CSRF token
                    return postData;
                },
                processResults: function (data, params) {
                    // Transform server response to Select2 format
                    params.page = params.page || 1;
                    
                    return {
                        results: data.map(function(item) {
                            return {
                                id: item.Value,
                                text: item.Text
                            };
                        }),
                        pagination: {
                            more: (params.page * 10) < data.length // Implement pagination if needed
                        }
                    };
                },
                cache: true // Cache results for better performance
            },
            escapeMarkup: function (markup) {
                return markup; // Let our markup through
            },
            templateResult: function (data) {
                if (data.loading) {
                    return data.text;
                }
                return data.text; // Customize result display if needed
            },
            templateSelection: function (data) {
                return data.text; // Customize selection display if needed
            }
        });
        
        // Handle selection events with special logic for "0" values
        element.on('select2:select', function (e) {
            var data = e.params.data;
            var currentValues = element.val() || [];
            
            // Special handling for "0" value (representing "none" or "all")
            if (data.id === "0") {
                // If "0" is selected, clear all other selections
                element.val(["0"]).trigger('change');
            } else {
                // If other items are selected, remove "0" from selection
                if (currentValues.indexOf("0") !== -1) {
                    var filteredValues = currentValues.filter(function(value) {
                        return value !== "0";
                    });
                    filteredValues.push(data.id);
                    element.val(filteredValues).trigger('change');
                }
            }
        });
        
        // Handle unselection events
        element.on('select2:unselect', function (e) {
            var currentValues = element.val() || [];
            
            // If no items are selected after unselecting, default to "0"
            if (currentValues.length === 0) {
                element.val(["0"]).trigger('change');
            }
        });
        
        // Handle clearing all selections
        element.on('select2:clear', function (e) {
            // Default to "0" if no items are selected
            element.val(["0"]).trigger('change');
        });
        
        // Preserve initial selected values
        var initialValues = element.val() || [];
        var initialOptions = [];
        
        // Read selected options from the underlying <select>
        element.find('option:selected').each(function () {
            initialOptions.push({
                id: $(this).val(),
                text: $(this).text()
            });
        });
        
        // Add initial options to Select2 if they're not already there
        initialOptions.forEach(function(option) {
            if (!element.find('option[value="' + option.id + '"]').length) {
                var newOption = new Option(option.text, option.id, true, true);
                element.append(newOption);
            }
        });
        
        // Ensure proper initial state
        if (initialValues.length === 0) {
            element.val(["0"]).trigger('change');
        } else {
            element.val(initialValues).trigger('change');
        }
    });
</script>