var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Portuguese = {
    weekdays: {
        shorthand: ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"],
        longhand: [
            "<PERSON>",
            "Segunda-feira",
            "Terça-feira",
            "Quarta-feira",
            "Q<PERSON>ta-feira",
            "Sexta-feira",
            "Sábado",
        ],
    },
    months: {
        shorthand: [
            "<PERSON>",
            "<PERSON>v",
            "<PERSON>",
            "Abr",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "Set",
            "Out",
            "Nov",
            "Dez",
        ],
        longhand: [
            "Janeiro",
            "Fevereiro",
            "Março",
            "Abril",
            "Maio",
            "Junho",
            "Julho",
            "Agosto",
            "Setembro",
            "Outubro",
            "Novembro",
            "Dezembro",
        ],
    },
    rangeSeparator: " até ",
    time_24hr: true,
};
fp.l10ns.pt = Portuguese;
export default fp.l10ns;
