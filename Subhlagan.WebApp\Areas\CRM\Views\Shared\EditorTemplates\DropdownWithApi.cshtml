﻿@model string?

<select asp-for="@Model" asp-items="@((IEnumerable<SelectListItem>)ViewData["SelectList"])"></select>

<script>
    $(document).ready(function () {
        // Enhanced Select2 Dropdown with API Integration (Open Source Replacement)
        var element = $("#@Html.IdForModel()");
        
        // Configure Select2 with comprehensive AJAX support
        element.select2({
            theme: 'bootstrap4', // Use Bootstrap 4 theme for consistency
            placeholder: 'Select an option...', // Better UX
            allowClear: true, // Allow clearing selection
            width: '100%', // Responsive width
            minimumInputLength: 1, // Start searching after 1 character
            ajax: {
                url: "@(Url.RouteUrl(ViewData["ApiUrl"].ToString()))",
                type: "POST",
                dataType: 'json',
                delay: 250, // Debounce API calls
                data: function (params) {
                    var postData = {
                        term: params.term || '', // Search term
                        page: params.page || 1   // Pagination support
                    };
                    
                    // Merge any additional Ajax parameters if provided
                    var additional = @Html.Raw(ViewData["AjaxParams"] ?? "{}");
                    $.extend(postData, additional);
                    
                    addAntiForgeryToken(postData); // Add CSRF token if necessary
                    return postData;
                },
                processResults: function (data, params) {
                    // Transform server response to Select2 format
                    params.page = params.page || 1;
                    
                    return {
                        results: data.map(function(item) {
                            return {
                                id: item.Value,
                                text: item.Text
                            };
                        }),
                        pagination: {
                            more: (params.page * 10) < data.length // Implement pagination if needed
                        }
                    };
                },
                cache: true // Cache results for better performance
            },
            escapeMarkup: function (markup) {
                return markup; // Let our markup through
            },
            templateResult: function (data) {
                if (data.loading) {
                    return data.text;
                }
                return data.text; // Customize result display if needed
            },
            templateSelection: function (data) {
                return data.text; // Customize selection display if needed
            }
        });
        
        // Handle selection change events
        element.on('select2:select', function (e) {
            var data = e.params.data;
            
            // If no item is selected, ensure empty value
            if (!data.id) {
                element.val('').trigger('change');
            }
            
            // If the search button id is provided, trigger its click automatically
            var searchButtonId = "@Html.Raw(ViewData["SearchButtonId"] ?? "")";
            if (searchButtonId && $("#" + searchButtonId).length > 0) {
                $("#" + searchButtonId).trigger("click");
            }
        });
        
        // Handle clearing selection
        element.on('select2:clear', function (e) {
            element.val('').trigger('change');
            
            // Trigger search button if configured
            var searchButtonId = "@Html.Raw(ViewData["SearchButtonId"] ?? "")";
            if (searchButtonId && $("#" + searchButtonId).length > 0) {
                $("#" + searchButtonId).trigger("click");
            }
        });
        
        // Preserve initial selected value
        var initialValue = element.val();
        var initialText = element.find('option:selected').text();
        
        if (initialValue && initialText) {
            // Add the initial option to Select2 if it's not already there
            var newOption = new Option(initialText, initialValue, true, true);
            element.append(newOption).trigger('change');
        }
    });
</script>
