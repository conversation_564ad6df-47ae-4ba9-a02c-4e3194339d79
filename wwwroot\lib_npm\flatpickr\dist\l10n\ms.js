(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.ms = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Malaysian = {
      weekdays: {
          shorthand: ["Aha", "Isn", "Sel", "Rab", "<PERSON>ha", "Ju<PERSON>", "Sab"],
          longhand: ["Ahad", "Isnin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Khamis", "Jumaat", "Sabtu"],
      },
      months: {
          shorthand: [
              "Jan",
              "Feb",
              "Mac",
              "Apr",
              "Mei",
              "Jun",
              "Jul",
              "Ogo",
              "Sep",
              "Okt",
              "Nov",
              "Di<PERSON>",
          ],
          longhand: [
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON>",
              "April",
              "<PERSON>",
              "<PERSON>",
              "<PERSON><PERSON>",
              "O<PERSON>",
              "September",
              "<PERSON><PERSON><PERSON>",
              "November",
              "Disember",
          ],
      },
      firstDayOfWeek: 1,
      ordinal: function () {
          return "";
      },
  };
  var ms = fp.l10ns;

  exports.Malaysian = Malaysian;
  exports.default = ms;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
