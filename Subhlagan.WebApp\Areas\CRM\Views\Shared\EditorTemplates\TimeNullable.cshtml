@model DateTime?
@{
    var fieldName = ViewData.TemplateInfo.GetFullHtmlFieldName(string.Empty);
    var displayId = fieldName + "_Display";
    var value = Model?.ToString("HH:mm", CultureInfo.InvariantCulture) ?? "";
    var postValue = Model?.ToString("HH:mm:ss") ?? "";
}

<!-- Hidden field to post ISO format to backend -->
<input type="hidden" id="@fieldName" name="@fieldName" value="@postValue" />

<!-- Flatpickr Time Picker for Nullable Time (Open Source Replacement) -->
<input id="@displayId" class="form-control flatpickr-input" type="text" placeholder="HH:mm" readonly="readonly" />

<script>
    $(document).ready(function () {
        // Initialize Flatpickr for time-only selection
        var initialValue = "@value";
        var picker = flatpickr("#@displayId", {
            enableTime: true,
            noCalendar: true, // Time only, no date
            dateFormat: "H:i", // 24-hour format
            time_24hr: true, // Use 24-hour format
            altInput: false, // We handle the hidden field manually
            allowInput: false, // Prevent manual typing to avoid format issues
            defaultDate: initialValue ? "1970-01-01 " + initialValue : null, // Handle nullable times with dummy date
            onChange: function(selectedDates, dateStr, instance) {
                if (selectedDates.length > 0) {
                    // Format time as HH:mm:ss for backend
                    var date = selectedDates[0];
                    var timeString = String(date.getHours()).padStart(2, '0') + ':' +
                                   String(date.getMinutes()).padStart(2, '0') + ':' +
                                   String(date.getSeconds()).padStart(2, '0');
                    $("#@fieldName").val(timeString);
                } else {
                    $("#@fieldName").val(""); // Clear the hidden field when picker is cleared
                }
            },
            onReady: function(selectedDates, dateStr, instance) {
                // Ensure hidden field is set correctly on initialization for nullable times
                if (selectedDates.length > 0) {
                    var date = selectedDates[0];
                    var timeString = String(date.getHours()).padStart(2, '0') + ':' +
                                   String(date.getMinutes()).padStart(2, '0') + ':' +
                                   String(date.getSeconds()).padStart(2, '0');
                    $("#@fieldName").val(timeString);
                } else {
                    $("#@fieldName").val(""); // Ensure empty value for null times
                }
            },
            // Allow clearing the time (important for nullable)
            wrap: false,
            clickOpens: true
        });

        // Add clear functionality with a clear button (essential for nullable times)
        var clearBtn = $('<button type="button" class="btn btn-outline-secondary btn-sm" style="margin-left: 5px;" title="Clear time"><i class="fas fa-times"></i></button>');
        $("#@displayId").after(clearBtn);
        
        clearBtn.on('click', function(e) {
            e.preventDefault();
            picker.clear();
            $("#@fieldName").val("");
        });
    });
</script>