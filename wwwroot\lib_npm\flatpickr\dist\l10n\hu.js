(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.hu = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Hungarian = {
      firstDayOfWeek: 1,
      weekdays: {
          shorthand: ["V", "H", "K", "<PERSON>z", "<PERSON>s", "P", "<PERSON><PERSON>"],
          longhand: [
              "<PERSON>as<PERSON>rna<PERSON>",
              "<PERSON><PERSON>tf<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>zer<PERSON>",
              "Csütörtök",
              "Péntek",
              "Szombat",
          ],
      },
      months: {
          shorthand: [
              "<PERSON>",
              "Feb",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>",
              "<PERSON>",
          ],
          longhand: [
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>ius",
              "Július",
              "Augusztus",
              "Szeptember",
              "Október",
              "November",
              "December",
          ],
      },
      ordinal: function () {
          return ".";
      },
      weekAbbreviation: "Hét",
      scrollTitle: "Görgessen",
      toggleTitle: "Kattintson a váltáshoz",
      rangeSeparator: " - ",
      time_24hr: true,
  };
  fp.l10ns.hu = Hungarian;
  var hu = fp.l10ns;

  exports.Hungarian = Hungarian;
  exports.default = hu;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
