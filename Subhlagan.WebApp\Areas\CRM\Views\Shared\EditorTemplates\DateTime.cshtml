@model DateTime
@{
    var fieldName = ViewData.TemplateInfo.GetFullHtmlFieldName(string.Empty);
    var displayId = fieldName + "_Display";
    var value = Model.ToString("dd/MM/yyyy hh:mm tt", CultureInfo.InvariantCulture);
    var postValue = Model.ToString("yyyy-MM-ddTHH:mm:ss");
}

<!-- Hidden field to post ISO format to backend -->
<input type="hidden" id="@fieldName" name="@fieldName" value="@postValue" />

<!-- Flatpickr DateTime Picker (Open Source Replacement) -->
@* <input id="@displayId" class="form-control flatpickr-input" type="text" placeholder="dd/mm/yyyy hh:mm AM/PM" readonly="readonly" /> *@

<div class="input-group">
    <input id="@displayId" class="form-control flatpickr-input" type="text" placeholder="dd/mm/yyyy hh:mm AM/PM" readonly="readonly" />
    <div class="input-group-append">
        <button type="button" class="btn" id="@(displayId)_clear">
            <i class="fas fa-times"></i>
        </button>
    </div>
</div>

<script>
    $(document).ready(function () {
        // Initialize Flatpickr with date and time enabled
        var picker = flatpickr("#@displayId", {
            enableTime: true,
            dateFormat: "d/m/Y h:i K", // K for AM/PM
            time_24hr: false, // Use 12-hour format with AM/PM
            altInput: false, // We handle the hidden field manually
            allowInput: false, // Prevent manual typing to avoid format issues
            defaultDate: "@value" || null,
            locale: {
                firstDayOfWeek: 1 // Monday as first day (common in many locales)
            },
            onChange: function(selectedDates, dateStr, instance) {
                if (selectedDates.length > 0) {
                    // Format datetime as ISO (yyyy-MM-ddTHH:mm:ss) for backend
                    var date = selectedDates[0];
                    var isoDateTime = date.getFullYear() + '-' + 
                                    String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                                    String(date.getDate()).padStart(2, '0') + 'T' +
                                    String(date.getHours()).padStart(2, '0') + ':' +
                                    String(date.getMinutes()).padStart(2, '0') + ':' +
                                    String(date.getSeconds()).padStart(2, '0');
                    $("#@fieldName").val(isoDateTime);
                } else {
                    $("#@fieldName").val(""); // Clear the hidden field when picker is cleared
                }
            },
            onReady: function(selectedDates, dateStr, instance) {
                // Ensure hidden field is set correctly on initialization
                if (selectedDates.length > 0) {
                    var date = selectedDates[0];
                    var isoDateTime = date.getFullYear() + '-' + 
                                    String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                                    String(date.getDate()).padStart(2, '0') + 'T' +
                                    String(date.getHours()).padStart(2, '0') + ':' +
                                    String(date.getMinutes()).padStart(2, '0') + ':' +
                                    String(date.getSeconds()).padStart(2, '0');
                    $("#@fieldName").val(isoDateTime);
                }
            },
            // Allow clearing the datetime
            wrap: false,
            clickOpens: true
        });

        // Add clear functionality with a clear button
        // var clearBtn = $('<button type="button" class="btn btn-outline-secondary btn-sm" style="margin-left: 5px;" title="Clear date and time"><i class="fas fa-times"></i></button>');
        // $("#@displayId").after(clearBtn);
        
        // clearBtn.on('click', function(e) {
        //     e.preventDefault();
        //     picker.clear();
        //     $("#@fieldName").val("");
        // });

        $("#@(displayId)_clear").on('click', function (e) {
            e.preventDefault();
            picker.clear();
            $("#@fieldName").val("");
        });
    });
</script>