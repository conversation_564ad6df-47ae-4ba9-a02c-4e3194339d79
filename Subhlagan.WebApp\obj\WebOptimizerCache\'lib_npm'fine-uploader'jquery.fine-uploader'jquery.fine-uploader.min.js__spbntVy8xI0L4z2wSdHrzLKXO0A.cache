{"Headers": {"Last-Modified": "Wed, 03 Mar 2021 09:36:32 GMT", "X-Content-Type-Options": "nosniff"}, "Body": "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", "CacheKey": "spbntVy8xI0L4z2wSdHrzLKXO0A"}