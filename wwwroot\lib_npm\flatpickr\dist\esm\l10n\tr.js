var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Turkish = {
    weekdays: {
        shorthand: ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>mt"],
        longhand: [
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "Çarşam<PERSON>",
            "<PERSON>şem<PERSON>",
            "Cuma",
            "Cumartesi",
        ],
    },
    months: {
        shorthand: [
            "<PERSON>ca",
            "Ş<PERSON>",
            "Mar",
            "<PERSON><PERSON>",
            "May",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
        ],
        longhand: [
            "<PERSON>cak",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "Ha<PERSON>ran",
            "Temmuz",
            "Ağustos",
            "Eylü<PERSON>",
            "Ekim",
            "Kasım",
            "Aralık",
        ],
    },
    firstDayOfWeek: 1,
    ordinal: function () {
        return ".";
    },
    rangeSeparator: " - ",
    weekAbbreviation: "Hf",
    scrollTitle: "Artırmak için kaydırın",
    toggleTitle: "Aç/Kapa",
    amPM: ["<PERSON><PERSON>", "ÖS"],
    time_24hr: true,
};
fp.l10ns.tr = Turkish;
export default fp.l10ns;
