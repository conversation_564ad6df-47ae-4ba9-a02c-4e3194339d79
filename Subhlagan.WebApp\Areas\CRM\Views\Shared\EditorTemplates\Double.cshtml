@model Double
<input asp-for="@Model"/>
@{
    var postfix = "";
    if (ViewData.ContainsKey("postfix") && ViewData["postfix"] != null)
    {
        postfix = ViewData["postfix"].ToString();
    }
    var culture = CultureInfo.CurrentCulture.TextInfo.IsRightToLeft ? CultureInfo.InvariantCulture : CultureInfo.CurrentCulture;
}
<script>
   $(document).ready(function () {
        // Enhanced HTML5 number input for Double with Cleave.js formatting (Open Source Replacement)
        var element = $("#@Html.IdForModel()");
        
        // Configure input as number type with step=0.0001 for doubles
        element.attr({
            'type': 'number',
            'step': '0.0001', // Support 4 decimal places
            'class': element.attr('class') + ' form-control numeric-input double-input'
        });
        
        // Optional: Add postfix display after the input
        var postfix = "@Html.Raw(postfix)";
        if (postfix) {
            element.after('<span class="input-group-text numeric-postfix">' + postfix + '</span>');
            element.wrap('<div class="input-group"></div>');
        }
        
        // Initialize Cleave.js for double number formatting
        new Cleave('#@Html.IdForModel()', {
            numeral: true,
            numeralDecimalMark: '.',
            numeralThousandsGroupStyle: 'thousand',
            numeralDecimalScale: 4, // Support 4 decimal places
            numeralPositiveOnly: false, // Allow negative numbers
            onValueChanged: function (e) {
                // Ensure the actual numeric value is maintained
                var numericValue = e.target.rawValue;
                element.attr('data-numeric-value', numericValue);
            }
        });
        
        // Handle form submission - ensure we submit the raw numeric value
        element.closest('form').on('submit', function() {
            var rawValue = element.attr('data-numeric-value') || element.val().replace(/[^\d.-]/g, '');
            element.val(rawValue);
        });
    });
</script>