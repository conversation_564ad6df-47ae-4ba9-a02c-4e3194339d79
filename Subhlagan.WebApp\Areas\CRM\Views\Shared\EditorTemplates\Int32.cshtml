﻿@model Int32
<input asp-for="@Model" />
@{
    var postfix = "";
    if (ViewData.ContainsKey("postfix") && ViewData["postfix"] != null)
    {
        postfix = ViewData["postfix"].ToString();
    }
    var culture = CultureInfo.CurrentCulture.TextInfo.IsRightToLeft ? CultureInfo.InvariantCulture : CultureInfo.CurrentCulture;
}
<script>
    $(document).ready(function() {
        // Enhanced HTML5 number input with Cleave.js formatting (Open Source Replacement)
        var element = $("#@Html.IdForModel()");
        
        // Configure input as number type with step=1 for integers
        element.attr({
            'type': 'number',
            'step': '1',
            'class': element.attr('class') + ' form-control numeric-input'
        });
        
        // Optional: Add Cleave.js for thousand separators and formatting
        var postfix = "@Html.Raw(postfix)";
        if (postfix) {
            // Add postfix display after the input
            element.after('<span class="input-group-text numeric-postfix">' + postfix + '</span>');
            element.wrap('<div class="input-group"></div>');
        }
        
        // Initialize Cleave.js for number formatting
        new Cleave('#@Html.IdForModel()', {
            numeral: true,
            numeralDecimalMark: '.',
            numeralThousandsGroupStyle: 'thousand',
            numeralDecimalScale: 0, // No decimals for integers
            numeralPositiveOnly: false, // Allow negative numbers
            onValueChanged: function (e) {
                // Ensure the actual numeric value is maintained
                var numericValue = e.target.rawValue;
                element.attr('data-numeric-value', numericValue);
            }
        });
        
        // Handle form submission - ensure we submit the raw numeric value
        element.closest('form').on('submit', function() {
            var rawValue = element.attr('data-numeric-value') || element.val().replace(/[^\d.-]/g, '');
            element.val(rawValue);
        });
    });
</script>