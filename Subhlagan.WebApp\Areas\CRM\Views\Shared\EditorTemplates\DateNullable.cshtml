﻿@model DateTime?
@{
    var fieldName = ViewData.TemplateInfo.GetFullHtmlFieldName(string.Empty);
    var displayId = fieldName + "_Display";
    var value = Model?.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture) ?? "";
    var postValue = Model?.ToString("yyyy-MM-dd") ?? "";
}

<!-- Hidden field to post ISO format to backend -->
<input type="hidden" id="@fieldName" name="@fieldName" value="@postValue" />

<!-- Flatpickr Date Picker for Nullable Date (Open Source Replacement) -->
@* <input id="@displayId" class="form-control flatpickr-input" type="text" placeholder="dd/mm/yyyy" readonly="readonly" /> *@

<!-- Flatpickr Date Picker with Clear Button (Open Source Replacement) -->
<div class="input-group">
    <input id="@displayId" class="form-control flatpickr-input" type="text" placeholder="dd/mm/yyyy" readonly="readonly" />
    <div class="input-group-append">
        <button type="button" class="btn" id="@(displayId)_clear">
            <i class="fas fa-times"></i>
        </button>
    </div>
</div>

<script>
    $(document).ready(function () {
        // Initialize Flatpickr for nullable date with comprehensive configuration
        var initialValue = "@value";
        var picker = flatpickr("#@displayId", {
            dateFormat: "d/m/Y",
            altInput: false, // We handle the hidden field manually
            allowInput: false, // Prevent manual typing to avoid format issues
            defaultDate: initialValue || null, // Handle nullable dates
            locale: {
                firstDayOfWeek: 1 // Monday as first day (common in many locales)
            },
            onChange: function(selectedDates, dateStr, instance) {
                if (selectedDates.length > 0) {
                    // Format date as ISO (yyyy-MM-dd) for backend
                    var isoDate = selectedDates[0].getFullYear() + '-' + 
                                String(selectedDates[0].getMonth() + 1).padStart(2, '0') + '-' + 
                                String(selectedDates[0].getDate()).padStart(2, '0');
                    $("#@fieldName").val(isoDate);
                } else {
                    $("#@fieldName").val(""); // Clear the hidden field when picker is cleared
                }
            },
            onReady: function(selectedDates, dateStr, instance) {
                // Ensure hidden field is set correctly on initialization for nullable dates
                if (selectedDates.length > 0) {
                    var isoDate = selectedDates[0].getFullYear() + '-' + 
                                String(selectedDates[0].getMonth() + 1).padStart(2, '0') + '-' + 
                                String(selectedDates[0].getDate()).padStart(2, '0');
                    $("#@fieldName").val(isoDate);
                } else {
                    $("#@fieldName").val(""); // Ensure empty value for null dates
                }
            },
            // Allow clearing the date (important for nullable)
            wrap: false,
            clickOpens: true
        });

        // Add clear functionality with a clear button (essential for nullable dates)
        // var clearBtn = $('<button type="button" class="btn btn-outline-secondary btn-sm" style="margin-left: 5px;" title="Clear date"><i class="fas fa-times"></i></button>');
        // $("#@displayId").after(clearBtn);
        
        // clearBtn.on('click', function(e) {
        //     e.preventDefault();
        //     picker.clear();
        //     $("#@fieldName").val("");
        // });


        // Add clear functionality to the existing clear button
        $("#@(displayId)_clear").on('click', function (e) {
            e.preventDefault();
            picker.clear();
            $("#@fieldName").val("");
        });
    });
</script>