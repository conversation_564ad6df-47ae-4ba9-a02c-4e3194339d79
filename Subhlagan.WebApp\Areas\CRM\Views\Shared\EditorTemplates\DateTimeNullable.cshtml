@model DateTime?
@{
    var fieldName = ViewData.TemplateInfo.GetFullHtmlFieldName(string.Empty);
    var displayId = fieldName + "_Display";
    var value = Model?.ToString("dd/MM/yyyy hh:mm tt", CultureInfo.InvariantCulture) ?? "";
    var postValue = Model?.ToString("yyyy-MM-ddTHH:mm:ss") ?? "";
}

<!-- Hidden field to post ISO format to backend -->
<input type="hidden" id="@fieldName" name="@fieldName" value="@postValue" />

<!-- Flatpickr DateTime Picker for Nullable DateTime (Open Source Replacement) -->
<input id="@displayId" class="form-control flatpickr-input" type="text" placeholder="dd/mm/yyyy hh:mm AM/PM" readonly="readonly" />

<script>
    $(document).ready(function () {
        // Initialize Flatpickr for nullable datetime with comprehensive configuration
        var initialValue = "@value";
        var picker = flatpickr("#@displayId", {
            enableTime: true,
            dateFormat: "d/m/Y h:i K", // K for AM/PM
            time_24hr: false, // Use 12-hour format with AM/PM
            altInput: false, // We handle the hidden field manually
            allowInput: false, // Prevent manual typing to avoid format issues
            defaultDate: initialValue || null, // Handle nullable datetimes
            locale: {
                firstDayOfWeek: 1 // Monday as first day (common in many locales)
            },
            onChange: function(selectedDates, dateStr, instance) {
                if (selectedDates.length > 0) {
                    // Format datetime as ISO (yyyy-MM-ddTHH:mm:ss) for backend
                    var date = selectedDates[0];
                    var isoDateTime = date.getFullYear() + '-' + 
                                    String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                                    String(date.getDate()).padStart(2, '0') + 'T' +
                                    String(date.getHours()).padStart(2, '0') + ':' +
                                    String(date.getMinutes()).padStart(2, '0') + ':' +
                                    String(date.getSeconds()).padStart(2, '0');
                    $("#@fieldName").val(isoDateTime);
                } else {
                    $("#@fieldName").val(""); // Clear the hidden field when picker is cleared
                }
            },
            onReady: function(selectedDates, dateStr, instance) {
                // Ensure hidden field is set correctly on initialization for nullable datetimes
                if (selectedDates.length > 0) {
                    var date = selectedDates[0];
                    var isoDateTime = date.getFullYear() + '-' + 
                                    String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                                    String(date.getDate()).padStart(2, '0') + 'T' +
                                    String(date.getHours()).padStart(2, '0') + ':' +
                                    String(date.getMinutes()).padStart(2, '0') + ':' +
                                    String(date.getSeconds()).padStart(2, '0');
                    $("#@fieldName").val(isoDateTime);
                } else {
                    $("#@fieldName").val(""); // Ensure empty value for null datetimes
                }
            },
            // Allow clearing the datetime (important for nullable)
            wrap: false,
            clickOpens: true
        });

        // Add clear functionality with a clear button (essential for nullable datetimes)
        var clearBtn = $('<button type="button" class="btn btn-outline-secondary btn-sm" style="margin-left: 5px;" title="Clear date and time"><i class="fas fa-times"></i></button>');
        $("#@displayId").after(clearBtn);
        
        clearBtn.on('click', function(e) {
            e.preventDefault();
            picker.clear();
            $("#@fieldName").val("");
        });
    });
</script>
